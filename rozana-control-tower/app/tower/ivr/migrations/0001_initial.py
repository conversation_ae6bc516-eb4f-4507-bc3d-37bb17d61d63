# Generated by Django 5.2.5 on 2025-09-01 19:30

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='IvrActions',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('action_type', models.Char<PERSON>ield(choices=[('tts', 'Text to Speech'), ('url', 'Audio URL')], max_length=10)),
                ('api_endpoint', models.URLField(blank=True, null=True)),
                ('api_method', models.CharField(choices=[('GET', 'GET'), ('POST', 'POST')], default='GET', max_length=10)),
                ('api_headers', models.J<PERSON><PERSON><PERSON>(blank=True, default=dict)),
                ('api_payload_template', models.J<PERSON>NField(blank=True, default=dict)),
                ('response_template', models.TextField()),
                ('operation_type', models.CharField(blank=True, choices=[('hangup', 'Hangup'), ('dial-users', 'Dial Users'), ('dial-numbers', 'Dial Numbers'), ('jump-node', 'Jump Node')], max_length=20, null=True)),
                ('operation_data', models.JSONField(blank=True, default=dict)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'ivr_actions',
            },
        ),
        migrations.CreateModel(
            name='UserCallHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('phonenumber', models.CharField(db_index=True, max_length=15)),
                ('call_id', models.CharField(db_index=True, max_length=100, unique=True)),
                ('created_at', models.DateTimeField(db_index=True, default=django.utils.timezone.now)),
            ],
            options={
                'db_table': 'user_call_history',
                'indexes': [models.Index(fields=['phonenumber', 'created_at'], name='user_call_h_phonenu_ac0014_idx')],
            },
        ),
        migrations.CreateModel(
            name='NodeActionMapping',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('node_id', models.CharField(db_index=True, max_length=50)),
                ('dtmf_input', models.CharField(blank=True, max_length=10, null=True)),
                ('condition_expr', models.JSONField(blank=True, default=dict)),
                ('priority', models.IntegerField(default=0)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('action', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='ivr.ivractions')),
            ],
            options={
                'db_table': 'node_action_mapping',
                'ordering': ['priority'],
                'indexes': [models.Index(fields=['node_id', 'dtmf_input', 'priority'], name='node_action_node_id_65468c_idx')],
            },
        ),
    ]
