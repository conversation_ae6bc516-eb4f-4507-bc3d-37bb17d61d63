import json
import logging
import requests
from datetime import datetime, timedelta
from django.utils import timezone
from django.conf import settings
from ..models import UserCallHistory, NodeActionMapping, IvrActions, Integrations


logger = logging.getLogger(__name__)


class IVRDecisionEngine:
    
    def process_request(self, node_id, caller_number, input_value, call_history):
        self._track_call_if_needed(node_id, caller_number, call_history)
        
        if self._should_redirect_to_customer_care(caller_number):
            return self._get_customer_care_response()
        
        mapping = self._find_node_action_mapping(node_id, input_value)
        
        if not mapping:
            return self._get_default_response()
        
        return self._execute_action(mapping.action, caller_number, input_value)
    
    def _track_call_if_needed(self, node_id, caller_number, call_history):
        tracking_nodes = getattr(settings, 'IVR_TRACKING_NODES', ['abc1'])
        
        if node_id in tracking_nodes:
            UserCallHistory.objects.get_or_create(
                call_id=call_history.call_id,
                defaults={
                    'phonenumber': caller_number,
                    'created_at': timezone.now()
                }
            )
    
    def _should_redirect_to_customer_care(self, caller_number):
        since = timezone.now() - timedelta(hours=24)
        call_count = UserCallHistory.objects.filter(
            phonenumber=caller_number,
            created_at__gte=since
        ).count()
        
        return call_count > 3
    
    def _find_node_action_mapping(self, node_id, input_value):
        mappings = NodeActionMapping.objects.filter(
            node_id=node_id,
            is_active=True
        ).select_related('action').order_by('priority')
        
        for mapping in mappings:
            if mapping.dtmf_input == input_value:
                return mapping
        
        for mapping in mappings:
            if mapping.dtmf_input is None:
                return mapping
        
        return None
    
    def _execute_action(self, action, caller_number, input_value):
        if action.integration or action.api_endpoint:
            api_response = self._make_api_call(action, caller_number, input_value)
            return self._format_response(action, api_response, caller_number)
        else:
            return self._format_static_response(action)
    
    def _make_api_call(self, action, caller_number, input_value):
        try:
            if action.integration:
                return self._call_integration_service(action, caller_number, input_value)
            
            payload = action.api_payload_template.copy()
            payload.update({
                'phone_number': caller_number,
                'input': input_value
            })
            
            headers = action.api_headers.copy()
            headers.setdefault('Content-Type', 'application/json')
            
            if action.api_method == 'POST':
                response = requests.post(
                    action.api_endpoint,
                    json=payload,
                    headers=headers,
                    timeout=10
                )
            else:
                response = requests.get(
                    action.api_endpoint,
                    params=payload,
                    headers=headers,
                    timeout=10
                )
            
            response.raise_for_status()
            return response.json()
            
        except Exception as e:
            logger.error(f"API call failed for action {action.name}: {str(e)}")
            return {'error': str(e)}
    
    def _format_response(self, action, api_response, caller_number):
        try:
            if action.response_mapping:
                return self._format_mapped_response(action, api_response)
            
            response_text = action.response_template
            
            if isinstance(api_response, dict):
                for key, value in api_response.items():
                    response_text = response_text.replace(f"{{{key}}}", str(value))
            
            response = {
                'action': action.action_type,
                'value': response_text
            }
            
            if action.operation_type:
                response['operation'] = action.operation_type
                if action.operation_data:
                    response['operation_data'] = action.operation_data
            
            return response
            
        except Exception as e:
            logger.error(f"Response formatting failed: {str(e)}")
            return self._get_error_response()
    
    def _call_integration_service(self, action, caller_number, input_value):
        from ..wrappers.ivr_wrapper import IntegrationService
        
        payload = action.api_payload_template.copy()
        payload.update({
            'phone_number': caller_number,
            'input': input_value
        })
        
        for key, value in payload.items():
            if isinstance(value, str):
                value = value.replace('{phone_number}', caller_number)
                value = value.replace('{input}', input_value)
                payload[key] = value
        
        service = IntegrationService()
        return service.call_service(
            action.integration,
            action.api_endpoint,
            action.api_method,
            payload,
            caller_number
        )
    
    def _format_mapped_response(self, action, api_response):
        from ..wrappers.ivr_wrapper import ResponseMapper
        
        if not api_response:
            return {
                'action': 'tts',
                'value': 'No data found'
            }
        
        mapper = ResponseMapper()
        mapped_data = mapper.map_response(action, api_response)
        
        response_text = action.response_template
        
        for key, value in mapped_data.items():
            response_text = response_text.replace(f"{{{key}}}", str(value))
        
        response = {
            'action': action.action_type,
            'value': response_text
        }
        
        if action.operation_type:
            response['operation'] = action.operation_type
            if action.operation_data:
                response['operation_data'] = action.operation_data
        
        return response
    
    def _format_static_response(self, action):
        response = {
            'action': action.action_type,
            'value': action.response_template
        }
        
        if action.operation_type:
            response['operation'] = action.operation_type
            if action.operation_data:
                response['operation_data'] = action.operation_data
        
        return response
    
    def _get_customer_care_response(self):
        return {
            'action': 'tts',
            'value': 'Connecting you to customer care',
            'operation': 'dial-numbers',
            'operation_data': {
                'numbers': ['+919876543210'],
                'dial_method': 'sequential'
            }
        }
    
    def _get_default_response(self):
        return {
            'action': 'tts',
            'value': 'Sorry, I did not understand. Please try again.'
        }
    
    def _get_error_response(self):
        return {
            'action': 'tts',
            'value': 'Sorry, we are experiencing technical difficulties. Please try again later.'
        }
