import json
import requests
import base64
from django.conf import settings
from ..models import Integrations


class IntegrationService:
    
    def call_service(self, integration, endpoint, method='GET', payload=None, phone_number=None):
        try:
            url = f"{integration.base_url.rstrip('/')}/{endpoint.lstrip('/')}"
            headers = self._get_auth_headers(integration)
            
            if phone_number and '{phone_number}' in url:
                url = url.replace('{phone_number}', phone_number)
            
            processed_payload = payload.copy() if payload else {}
            if phone_number:
                for key, value in processed_payload.items():
                    if isinstance(value, str) and '{phone_number}' in value:
                        processed_payload[key] = value.replace('{phone_number}', phone_number)
            
            if method == 'POST':
                response = requests.post(url, json=processed_payload, headers=headers, timeout=10)
            else:
                response = requests.get(url, params=processed_payload, headers=headers, timeout=10)
            
            response.raise_for_status()
            return response.json()
            
        except Exception as e:
            return {'error': str(e)}
    
    def _get_auth_headers(self, integration):
        headers = {'Content-Type': 'application/json'}
        
        if integration.auth_type == 'basic':
            credentials = base64.b64encode(f"{integration.username}:{integration.secret}".encode()).decode()
            headers['Authorization'] = f'Basic {credentials}'
        elif integration.auth_type == 'token':
            if integration.secret.startswith('Bearer '):
                headers['Authorization'] = integration.secret
            else:
                headers['Authorization'] = integration.secret
        elif integration.auth_type == 'oauth':
            headers['Authorization'] = f'Bearer {integration.secret}'
        
        return headers


class ResponseMapper:
    
    def map_response(self, action, api_response):
        if not action.response_mapping or not api_response:
            return api_response
        
        mapped_data = {}
        
        for key, path in action.response_mapping.items():
            value = self._extract_value(api_response, path)
            mapped_data[key] = value
        
        return mapped_data
    
    def _extract_value(self, data, path):
        if not path:
            return None
        
        keys = path.split('.')
        current = data
        
        for key in keys:
            if isinstance(current, dict) and key in current:
                current = current[key]
            elif isinstance(current, list) and key.isdigit():
                index = int(key)
                if 0 <= index < len(current):
                    current = current[index]
                else:
                    return None
            else:
                return None
        
        return current
