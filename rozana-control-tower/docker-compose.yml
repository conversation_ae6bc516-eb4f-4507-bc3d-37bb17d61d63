services:
  db1:
    image: postgres:15
    volumes:
      - postgres_data:/var/lib/postgresql/data/
    environment:
      - POSTGRES_DB=tower
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=rozana^1234
    ports:
      - "5437:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 5s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7-alpine
    ports:
      - "6381:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 3s
      retries: 5

  api:
    container_name: tower_api
    build: .
    volumes:
      - ./app/tower:/application
    ports:
      - "8002:8000"
    env_file:
      - .env
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8000/admin/ || exit 1"]
      interval: 10s
      timeout: 5s
      retries: 5
    depends_on:
      db1:
        condition: service_healthy
      redis:
        condition: service_healthy
    stdin_open: true
    tty: true
    command: python manage.py runserver 0.0.0.0:8000

volumes:
  postgres_data:
