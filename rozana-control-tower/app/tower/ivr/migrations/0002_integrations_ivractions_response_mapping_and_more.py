# Generated by Django 5.2.5 on 2025-09-01 20:14

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('ivr', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Integrations',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('base_url', models.URLField()),
                ('auth_type', models.CharField(choices=[('basic', 'Basic Auth'), ('oauth', 'OAuth'), ('token', 'Token')], max_length=10)),
                ('username', models.CharField(blank=True, max_length=255, null=True)),
                ('secret', models.TextField(blank=True, null=True)),
                ('is_active', models.<PERSON><PERSON>an<PERSON>ield(default=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'integrations',
            },
        ),
        migrations.AddField(
            model_name='ivractions',
            name='response_mapping',
            field=models.JSONField(blank=True, default=dict),
        ),
        migrations.AlterField(
            model_name='ivractions',
            name='api_endpoint',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='ivractions',
            name='integration',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='ivr.integrations'),
        ),
    ]
