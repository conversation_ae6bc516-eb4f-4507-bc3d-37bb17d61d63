from django.contrib import admin
from .models import UserCallHistory, IvrActions, NodeActionMapping, Integrations


@admin.register(UserCallHistory)
class UserCallHistoryAdmin(admin.ModelAdmin):
    list_display = ['phonenumber', 'call_id', 'created_at']
    list_filter = ['created_at']
    search_fields = ['phonenumber', 'call_id']
    readonly_fields = ['created_at']


@admin.register(Integrations)
class IntegrationsAdmin(admin.ModelAdmin):
    list_display = ['name', 'base_url', 'auth_type', 'is_active', 'created_at']
    list_filter = ['auth_type', 'is_active']
    search_fields = ['name', 'base_url']
    readonly_fields = ['created_at', 'updated_at']


@admin.register(IvrActions)
class IvrActionsAdmin(admin.ModelAdmin):
    list_display = ['name', 'action_type', 'integration', 'operation_type', 'is_active', 'created_at']
    list_filter = ['action_type', 'operation_type', 'is_active']
    search_fields = ['name']
    readonly_fields = ['created_at', 'updated_at']


@admin.register(NodeActionMapping)
class NodeActionMappingAdmin(admin.ModelAdmin):
    list_display = ['node_id', 'action', 'dtmf_input', 'priority', 'is_active']
    list_filter = ['is_active', 'action__action_type']
    search_fields = ['node_id', 'action__name']
    readonly_fields = ['created_at']
