from django.db import models
from django.utils import timezone


class UserCallHistory(models.Model):
    phonenumber = models.CharField(max_length=15, db_index=True)
    call_id = models.CharField(max_length=100, unique=True, db_index=True)
    created_at = models.DateTimeField(default=timezone.now, db_index=True)
    
    class Meta:
        db_table = 'user_call_history'
        indexes = [
            models.Index(fields=['phonenumber', 'created_at']),
        ]


class Integrations(models.Model):
    AUTH_TYPES = [
        ('basic', 'Basic Auth'),
        ('oauth', 'OAuth'),
        ('token', 'Token'),
    ]
    
    name = models.CharField(max_length=100, unique=True)
    base_url = models.URLField()
    auth_type = models.CharField(max_length=10, choices=AUTH_TYPES)
    username = models.Char<PERSON>ield(max_length=255, null=True, blank=True)
    secret = models.TextField(null=True, blank=True)
    is_active = models.Boolean<PERSON>ield(default=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'integrations'


class IvrActions(models.Model):
    ACTION_TYPES = [
        ('tts', 'Text to Speech'),
        ('url', 'Audio URL'),
    ]
    
    OPERATION_TYPES = [
        ('hangup', 'Hangup'),
        ('dial-users', 'Dial Users'),
        ('dial-numbers', 'Dial Numbers'),
        ('jump-node', 'Jump Node'),
    ]
    
    name = models.CharField(max_length=100, unique=True)
    action_type = models.CharField(max_length=10, choices=ACTION_TYPES)
    integration = models.ForeignKey(Integrations, on_delete=models.CASCADE, null=True, blank=True)
    api_endpoint = models.CharField(max_length=255, null=True, blank=True)
    api_method = models.CharField(max_length=10, default='GET', choices=[('GET', 'GET'), ('POST', 'POST')])
    api_headers = models.JSONField(default=dict, blank=True)
    api_payload_template = models.JSONField(default=dict, blank=True)
    response_template = models.TextField()
    response_mapping = models.JSONField(default=dict, blank=True)
    operation_type = models.CharField(max_length=20, choices=OPERATION_TYPES, null=True, blank=True)
    operation_data = models.JSONField(default=dict, blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'ivr_actions'


class NodeActionMapping(models.Model):
    node_id = models.CharField(max_length=50, db_index=True)
    action = models.ForeignKey(IvrActions, on_delete=models.CASCADE)
    dtmf_input = models.CharField(max_length=10, null=True, blank=True)
    condition_expr = models.JSONField(default=dict, blank=True)
    priority = models.IntegerField(default=0)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(default=timezone.now)
    
    class Meta:
        db_table = 'node_action_mapping'
        indexes = [
            models.Index(fields=['node_id', 'dtmf_input', 'priority']),
        ]
        ordering = ['priority']
