import json
import logging
from datetime import datetime, timedelta
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.utils.decorators import method_decorator
from django.views import View
from django.utils import timezone
from .models import UserCallHistory, NodeActionMapping, IvrActions
from .middleware.decision_engine import IVRDecisionEngine

logger = logging.getLogger(__name__)


@method_decorator(csrf_exempt, name='dispatch')
@method_decorator(require_http_methods(["POST"]), name='dispatch')
class MyOperatorInputNodeView(View):
    
    def post(self, request):
        try:
            data = json.loads(request.body)
            
            # Extract required fields
            uid = data.get('uid')
            node_id = data.get('node_id')
            timestamp = data.get('timestamp')
            clid = data.get('clid')  # caller phone number
            input_value = data.get('input', '')
            company_id = data.get('company_id')
            
            # Validate required fields
            if not all([uid, node_id, clid]):
                return JsonResponse({
                    'error': 'Missing required fields: uid, node_id, clid'
                }, status=400)
            
            # Log call history
            call_history, created = UserCallHistory.objects.get_or_create(
                call_id=uid,
                defaults={
                    'phonenumber': clid,
                    'created_at': timezone.now()
                }
            )
            
            # Initialize decision engine
            decision_engine = IVRDecisionEngine()
            
            # Process the request and get response
            response_data = decision_engine.process_request(
                node_id=node_id,
                caller_number=clid,
                input_value=input_value,
                call_history=call_history
            )
            
            logger.info(f"IVR Response for uid={uid}, node_id={node_id}: {response_data}")
            
            return JsonResponse(response_data)
            
        except json.JSONDecodeError:
            return JsonResponse({'error': 'Invalid JSON'}, status=400)
        except Exception as e:
            logger.error(f"IVR processing error: {str(e)}")
            return JsonResponse({
                'action': 'tts',
                'value': 'Sorry, we are experiencing technical difficulties. Please try again later.'
            }, status=500)
