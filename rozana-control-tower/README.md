# 🏗️ Rozana Control Tower

> **"Orchestrating Excellence, One Service at a Time"**  
> *Central Command & Communication Hub for the Rozana Ecosystem*

[![Python](https://img.shields.io/badge/Python-3.12+-blue.svg)](https://www.python.org/downloads/)
[![Django](https://img.shields.io/badge/Django-5.2+-green.svg)](https://www.djangoproject.com/)
[![Docker](https://img.shields.io/badge/Docker-Ready-blue.svg)](https://www.docker.com/)
[![PostgreSQL](https://img.shields.io/badge/PostgreSQL-15+-blue.svg)](https://www.postgresql.org/)

---

## 🎯 Mission Statement

**Rozana Control Tower** serves as the **central command and communication hub** for the entire Rozana ecosystem. Like an airport control tower that coordinates flights, manages traffic, and ensures smooth operations, our Control Tower orchestrates inter-service communication, manages system-wide operations, and provides unified oversight across all Rozana services.

## 🌟 Vision

*"To be the single source of truth and coordination for all Rozana services, ensuring seamless communication, operational excellence, and system-wide visibility."*

---

## 🚀 Key Features

### 🎛️ **Central Command Center**
- **Service Orchestration**: Coordinate operations across multiple Rozana services
- **Communication Hub**: Facilitate secure inter-service communication
- **System Monitoring**: Real-time visibility into ecosystem health and performance

### 📡 **Communication Management**
- **IVR Center**: Intelligent voice response and call routing
- **Cross-Service Messaging**: Unified messaging across the platform
- **Event Broadcasting**: System-wide event coordination

### 🔧 **Operational Excellence**
- **Configuration Management**: Centralized configuration for all services
- **Health Monitoring**: Service availability and performance tracking
- **Alert Management**: Proactive incident detection and notification

---

## 🏗️ Architecture

```
┌─────────────────────────────────────────────────────────┐
│                 🏗️ CONTROL TOWER                        │
│                                                         │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐     │
│  │   IVR       │  │   Admin     │  │  Monitoring │     │
│  │  Center     │  │   Panel     │  │   Center    │     │
│  └─────────────┘  └─────────────┘  └─────────────┘     │
│                                                         │
│  ┌─────────────────────────────────────────────────────┐ │
│  │           Communication Layer                       │ │
│  └─────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────┘
                              │
              ┌───────────────┼───────────────┐
              │               │               │
    ┌─────────▼─────────┐ ┌───▼────┐ ┌───────▼──────┐
    │     Potions       │ │  OMS   │ │   Wallet     │
    │    Service        │ │Service │ │   Service    │
    └───────────────────┘ └────────┘ └──────────────┘
```

---

## 🛠️ Technology Stack

| Component | Technology | Version |
|-----------|------------|---------|
| **Backend** | Django | 5.2+ |
| **Database** | PostgreSQL | 15+ |
| **Cache/Queue** | Redis | 7+ |
| **Container** | Docker | Latest |
| **Language** | Python | 3.12+ |

---

## 🚀 Quick Start

### Prerequisites
- Docker & Docker Compose
- Python 3.12+
- PostgreSQL 15+

### 🐳 Docker Setup (Recommended)

1. **Clone the Repository**
   ```bash
   git clone <repository-url>
   cd rozana-control-tower
   ```

2. **Environment Configuration**
   ```bash
   cp sample.env .env
   # Edit .env with your configuration
   ```

3. **Launch Control Tower**
   ```bash
   docker-compose up -d
   ```

4. **Access Services**
   - **API**: http://localhost:8001
   - **Admin Panel**: http://localhost:8001/admin/
   - **Database**: localhost:5433
   - **Redis**: localhost:6381

### 🔧 Manual Setup

1. **Create Virtual Environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # Linux/Mac
   # or
   venv\Scripts\activate     # Windows
   ```

2. **Install Dependencies**
   ```bash
   pip install -r app/requirements.txt
   ```

3. **Database Setup**
   ```bash
   cd app/tower
   python manage.py migrate
   python manage.py createsuperuser
   ```

4. **Run Development Server**
   ```bash
   python manage.py runserver 0.0.0.0:8000
   ```

---

## ⚙️ Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `DATABASE_NAME` | PostgreSQL database name | `tower` |
| `DATABASE_USER` | Database username | `postgres` |
| `DATABASE_PASSWORD` | Database password | `rozana^1234` |
| `DATABASE_HOST` | Database host | `db1` |
| `DATABASE_PORT` | Database port | `5432` |
| `DEBUG` | Debug mode | `1` |

### Database Configuration

Control Tower supports multiple database connections:
- **Primary**: Control Tower's own database
- **Potions**: Access to Potions service data
- **Cross-Service**: Ability to query other Rozana services

---

## 📋 Services Overview

### 🎯 Core Components

| Component | Purpose | Status |
|-----------|---------|--------|
| **IVR Centre** | Call routing and voice response | ✅ Active |
| **Admin Panel** | System administration interface | ✅ Active |
| **API Gateway** | Service communication endpoint | ✅ Active |
| **Health Monitor** | System health tracking | 🚧 Planned |

---

## 🔒 Security

- **Environment Variables**: All sensitive data managed via environment variables
- **Database Security**: PostgreSQL with secure connection protocols
- **Docker Security**: Non-root user execution in containers
- **Access Control**: Django's built-in authentication and authorization

---

## 📊 Monitoring & Health

### Health Checks
- **Database**: PostgreSQL connection monitoring
- **Redis**: Cache service availability
- **Application**: Django application health

### Docker Health Checks
```yaml
healthcheck:
  test: ["CMD-SHELL", "curl -f http://localhost:8000/admin/ || exit 1"]
  interval: 10s
  timeout: 5s
  retries: 5
```

---

## 🤝 Contributing

1. **Fork** the repository
2. **Create** a feature branch (`git checkout -b feature/amazing-feature`)
3. **Commit** your changes (`git commit -m 'Add amazing feature'`)
4. **Push** to the branch (`git push origin feature/amazing-feature`)
5. **Open** a Pull Request

### Development Standards
- Follow [PEP 8](https://pep8.org/) coding standards
- Write comprehensive tests
- Update documentation for new features
- Use meaningful commit messages

---

## 📝 License

This project is proprietary to Rozana. All rights reserved.

---

## 🆘 Support

- **Technical Issues**: Create an issue in the repository
- **Documentation**: Check the `/docs` directory
- **Team Contact**: Rozana Engineering Team

---

## 🎉 Acknowledgments

Built with ❤️ by the **Rozana Engineering Team**

*"In the sky of distributed systems, Control Tower ensures every service lands safely."*

---

<div align="center">

**[🏠 Home](/) | [📚 Documentation](/docs) | [🐛 Issues](/issues) | [🚀 Releases](/releases)**

Made with ⚡ by [Rozana](https://rozana.in)

</div>
